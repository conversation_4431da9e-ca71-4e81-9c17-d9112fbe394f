# 🚀 Cal.com Deployment Configuration for UpZera

## ✅ Supabase Project Created
- **Project ID**: `ikprwxccqnxcltbhjakk`
- **Project Name**: UpZera Cal.com Scheduling
- **Region**: eu-central-1
- **Status**: ACTIVE_HEALTHY
- **Database Host**: db.ikprwxccqnxcltbhjakk.supabase.co

## 🔧 Environment Variables for Cal.com Deployment

### Database Configuration
```env
# Supabase Database URLs
DATABASE_URL="postgresql://postgres:UpZera2025!CalCom#<EMAIL>:5432/postgres"
DATABASE_DIRECT_URL="postgresql://postgres:UpZera2025!CalCom#<EMAIL>:5432/postgres"
```

### NextAuth Configuration
```env
NEXTAUTH_SECRET="cal-com-upzera-2025-secure-secret-key-for-auth"
NEXTAUTH_URL="https://your-cal-domain.vercel.app"
```

### App URLs (Update after Vercel deployment)
```env
NEXT_PUBLIC_WEBAPP_URL="https://your-cal-domain.vercel.app"
NEXT_PUBLIC_WEBSITE_URL="https://your-cal-domain.vercel.app"
```

### Encryption
```env
CALENDSO_ENCRYPTION_KEY="upzera-cal-com-encryption-key-2025-secure"
```

### Email Configuration (Using your existing Mailgun)
```env
EMAIL_FROM="<EMAIL>"
EMAIL_SERVER_HOST="smtp.eu.mailgun.org"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-mailgun-smtp-password"
```

### Branding
```env
NEXT_PUBLIC_APP_NAME="UpZera Scheduling"
NEXT_PUBLIC_SUPPORT_MAIL_ADDRESS="<EMAIL>"
NEXT_PUBLIC_COMPANY_NAME="UpZera"
NEXT_PUBLIC_DISABLE_SIGNUP=true
```

### Google Calendar (To be configured)
```env
GOOGLE_API_CREDENTIALS='{"web":{"client_id":"YOUR_CLIENT_ID","client_secret":"YOUR_CLIENT_SECRET","redirect_uris":["https://your-cal-domain.vercel.app/api/integrations/googlecalendar/callback"]}}'
GOOGLE_LOGIN_ENABLED=false
```

## 🚀 Deployment Steps

### Step 1: Deploy to Vercel (One-Click)
1. Go to: https://vercel.com/new/clone?repository-url=https://github.com/calcom/cal.com
2. Click "Deploy"
3. Choose a project name: `upzera-cal-com`
4. Add all environment variables above
5. Deploy

### Step 2: Set up Google Calendar Integration
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create new project: "UpZera Cal.com"
3. Enable Google Calendar API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `https://upzera-cal-com.vercel.app/api/integrations/googlecalendar/callback`
6. Update `GOOGLE_API_CREDENTIALS` in Vercel environment variables

### Step 3: Initialize Database
After deployment, run these commands in Vercel terminal:
```bash
npx prisma migrate deploy
npx prisma db seed
```

### Step 4: Update UpZera Integration
Update these files with your deployed URL:
- `project/app/contact/page.tsx`
- `project/components/chatbot/ChatMessage.tsx`

Replace:
```typescript
calOrigin="https://cal.com"
calLink="bailey/15min"
```

With:
```typescript
calOrigin="https://upzera-cal-com.vercel.app"
calLink="edgaras/30min"
```

## 📋 Next Actions Required:
1. ✅ Supabase database created
2. 🔄 Deploy to Vercel with environment variables
3. ⏳ Set up Google Calendar API
4. ⏳ Run database migrations
5. ⏳ Update UpZera integration links
6. ⏳ Test booking functionality

## 🔗 Important Links:
- **Supabase Project**: https://supabase.com/dashboard/project/ikprwxccqnxcltbhjakk
- **Vercel Deployment**: https://vercel.com/new/clone?repository-url=https://github.com/calcom/cal.com
- **Google Cloud Console**: https://console.cloud.google.com

## 🛠️ Troubleshooting:
- If database connection fails, check Supabase project status
- If OAuth fails, verify Google Cloud redirect URIs
- If booking fails, check Prisma migrations status
