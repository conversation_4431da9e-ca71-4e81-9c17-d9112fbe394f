# 🚀 Cal.com Free Account Setup Guide for UpZera

## ✅ What's Already Done
- ✅ Cal.com embed package installed (`@calcom/embed-react`)
- ✅ Contact page configured with Cal.com embed
- ✅ Chatbot configured with Cal.com embed
- ✅ UpZera purple branding color (`#7c3aed`) applied
- ✅ Calendly integration removed/commented out

## 🎯 Next Steps (5 minutes setup)

### Step 1: Create Your Free Cal.com Account
1. **Go to [cal.com](https://cal.com)**
2. **Sign up with your business email** (e.g., <EMAIL>)
3. **Choose a username** (e.g., `upzera`, `edgaras-upzera`, or `upzera-consulting`)
4. **Complete the onboarding process**

### Step 2: Set Up Your Event Type
1. **Create a new event type:**
   - Name: "30 Minute Consultation"
   - Duration: 30 minutes
   - Description: "Schedule a consultation with UpZera team"

2. **Configure availability:**
   - Set your working hours
   - Set your timezone
   - Configure buffer times if needed

3. **Set up meeting location:**
   - Add your preferred meeting method (Zoom, Google Meet, etc.)
   - Or set it to "Ask invitee" to let them choose

### Step 3: Customize Branding (Optional but Recommended)
1. **Go to Settings → Appearance in your Cal.com dashboard**
2. **Set brand color:** `#7c3aed` (UpZera purple)
3. **Upload logo:** Use your UpZera logo if desired
4. **Company name:** "UpZera"

### Step 4: Update Your UpZera Website
Once you have your username and event type set up:

1. **Replace `YOUR_USERNAME` in these files:**
   - `app/contact/page.tsx` (line 764)
   - `components/chatbot/ChatMessage.tsx` (line 319)

2. **Update the calLink to match your setup:**
   ```typescript
   calLink="your-actual-username/30min"
   ```

### Step 5: Test the Integration
1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Test both locations:**
   - Visit `/contact` page - check the calendar embed
   - Open chatbot and trigger calendar booking
   - Verify Cal.com embed loads with UpZera purple branding

## 🎨 Branding Features Available (Free Plan)

### ✅ What You Can Customize (Free):
- ✅ Brand color (already set to UpZera purple `#7c3aed`)
- ✅ Company name and description
- ✅ Event type names and descriptions
- ✅ Availability settings
- ✅ Meeting locations and instructions
- ✅ Email notifications (basic)

### 💎 Premium Features (Not Needed):
- Custom domain (e.g., book.upzera.com)
- Remove "Powered by Cal.com" (minimal branding anyway)
- Advanced workflows and integrations
- Team scheduling features

## 🔧 Configuration Details

### Current Embed Configuration:
```typescript
// Contact Page (Dark Theme)
<Cal
  calOrigin="https://cal.com"
  calLink="YOUR_USERNAME/30min"
  config={{
    "theme": "dark",
    "branding": {
      "brandColor": "#7c3aed"
    }
  }}
/>

// Chatbot (Light Theme)
<Cal
  calOrigin="https://cal.com"
  calLink="YOUR_USERNAME/30min"
  config={{
    "theme": "light",
    "branding": {
      "brandColor": "#7c3aed"
    }
  }}
/>
```

## 🚀 Benefits of This Approach

### ✅ Advantages:
- **Free forever** - No monthly fees like Calendly
- **Quick setup** - 5 minutes vs hours of self-hosting
- **Reliable hosting** - Cal.com handles all infrastructure
- **Automatic updates** - Always latest features and security
- **UpZera branding** - Purple color matches your website
- **Mobile responsive** - Works perfectly on all devices
- **No "Powered by" branding** - Clean, professional appearance

### 🎯 Perfect For:
- Small to medium businesses
- Quick replacement of Calendly
- Professional booking without monthly costs
- Maintaining brand consistency

## 📞 Support
If you need help with the setup, the Cal.com community is very responsive, and their documentation is excellent at [docs.cal.com](https://docs.cal.com).

---

**Ready to go live?** Just update the `YOUR_USERNAME` placeholder with your actual Cal.com username and you're done! 🎉
